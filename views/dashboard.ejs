<!-- dashboard.ejs -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard | <%= company.name %></title>

    <!-- DNS Prefetch for external resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//fonts.gstatic.com" />
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />
    <link rel="dns-prefetch" href="//unpkg.com" />

    <!-- Preconnect for critical external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Preload critical font to prevent layout shift -->
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v13/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Optimized Font Loading with font-display swap -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Font fallback to prevent invisible text -->
    <style>
      @font-face {
        font-family: 'Inter';
        font-display: swap;
        src: local('Inter'), local('Inter-Regular');
      }
    </style>

    <!-- Critical CSS Inlined for Fastest Rendering -->
    <style>
      /* Critical CSS - Above the fold styles */
      :root {
        --primary-color: #6366f1;
        --secondary-color: #818cf8;
        --success-color: #22c55e;
        --danger-color: #ef4444;
        --background-color: #f8fafc;
        --card-background: #ffffff;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      html {
        font-size: 16px;
        line-height: 1.5;
      }

      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: var(--background-color);
        color: var(--text-primary);
        overflow-x: hidden;
      }

      /* Layout structure - critical for initial render */
      .layout-wrapper {
        display: flex;
        min-height: 100vh;
        width: 100%;
      }

      .content-wrapper {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
      }

      .main-container {
        flex: 1;
        margin-left: 280px;
        margin-top: 80px;
        padding: 24px;
        width: calc(100vw - 280px);
        min-height: calc(100vh - 80px);
        transition: all 0.3s ease;
        position: relative;
      }

      /* Critical header styles */
      .header {
        position: fixed;
        top: 0;
        right: 0;
        left: 280px;
        height: 80px;
        background: var(--card-background);
        border-bottom: 1px solid var(--border-color);
        z-index: 1000;
        display: flex;
        align-items: center;
        padding: 0 24px;
      }

      /* Critical sidebar styles */
      .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        width: 280px;
        height: 100vh;
        background: var(--card-background);
        border-right: 1px solid var(--border-color);
        z-index: 1001;
        overflow-y: auto;
      }

      /* Critical greeting card styles */
      .greeting-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
      }

      .greeting-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .greeting-text h1 {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }

      .greeting-text p {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      /* Critical grid layout */
      .main-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
        width: 100%;
        margin-bottom: 2rem;
      }

      /* Critical card styles */
      .dashboard-card {
        background: var(--card-background);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--border-color);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
      }

      .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        color: white;
        background: var(--primary-color);
      }

      .card-content h3 {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .card-stats {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
      }

      /* Prevent layout shift by reserving space for dynamic content */
      .payroll-calendar-card {
        min-height: 400px; /* Reserve space for calendar */
      }

      .companies-grid {
        min-height: 200px; /* Reserve space for company cards */
      }

      .chart-container {
        min-height: 300px; /* Reserve space for charts */
        width: 100%;
      }

      /* Ensure chart containers have fixed dimensions */
      #payrollCalendar {
        height: 400px !important;
        width: 100% !important;
      }

      .apexcharts-canvas {
        height: 300px !important;
        width: 100% !important;
      }

      /* Font loading optimization to prevent text reflow */
      .greeting-text h1,
      .card-content h3,
      .number {
        font-display: swap;
        /* Reserve space for text to prevent reflow */
        min-height: 1.2em;
      }

      /* Prevent image layout shifts */
      img {
        height: auto;
        max-width: 100%;
      }

      /* Ensure consistent card heights */
      .dashboard-card {
        min-height: 120px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    </style>

    <!-- Load critical CSS synchronously to prevent layout shift -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <!-- Load less critical CSS asynchronously -->
    <script>
      // Function to load CSS asynchronously without layout shift
      function loadCSS(href) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = 'print';
        link.onload = function() {
          this.media = 'all';
        };
        document.head.appendChild(link);
      }

      // Load only truly non-critical CSS files
      loadCSS('/css/onboarding.css');
      loadCSS('/css/mobile-nav.css');
      loadCSS('/css/toast.css');
      loadCSS('/css/event-modals.css');
    </script>

    <!-- Load dashboard CSS synchronously to prevent layout shift -->
    <link rel="stylesheet" href="/css/dashboard.css" />

    <!-- Fallback for users with JavaScript disabled -->
    <noscript>
      <link rel="stylesheet" href="/css/onboarding.css" />
      <link rel="stylesheet" href="/css/mobile-nav.css" />
      <link rel="stylesheet" href="/css/toast.css" />
      <link rel="stylesheet" href="/css/event-modals.css" />
    </noscript>
    <!-- Performance mark for measuring load time -->
    <script>
      performance.mark('head-end');

      // Early error handling
      window.addEventListener('error', function(e) {
        console.error('Early error:', e.error);
      });

      // Critical JavaScript for immediate functionality
      window.PandaPayroll = window.PandaPayroll || {};
      window.PandaPayroll.config = {
        csrfToken: '<%= typeof csrfToken !== "undefined" ? csrfToken : "" %>',
        userId: '<%= typeof user !== "undefined" && user ? user._id : "" %>',
        companyCode: '<%= typeof company !== "undefined" ? company.companyCode : "" %>'
      };

      // Prevent layout shift by setting container dimensions early
      document.addEventListener('DOMContentLoaded', function() {
        // Set fixed dimensions for chart containers to prevent layout shift
        const calendarEl = document.getElementById('payrollCalendar');
        if (calendarEl) {
          calendarEl.style.height = '400px';
          calendarEl.style.width = '100%';
          calendarEl.style.minHeight = '400px';
        }

        // Set dimensions for any chart containers
        const chartContainers = document.querySelectorAll('.chart-container, .apexcharts-canvas');
        chartContainers.forEach(container => {
          container.style.height = '300px';
          container.style.width = '100%';
          container.style.minHeight = '300px';
        });

        // Reserve space for dynamic content
        const companyGrid = document.querySelector('.companies-grid');
        if (companyGrid) {
          companyGrid.style.minHeight = '200px';
        }
      });
    </script>
  </head>
  <body>
    <div class="layout-wrapper">
      <%- include('partials/sidebar', { user: user, company: company }) %>
      <div class="content-wrapper">
        <%- include('partials/header', { user: user }) %>

        <main class="main-container">
          <!-- Greeting Card -->
          <div class="greeting-card">
            <div class="greeting-content">
              <div class="greeting-text">
                <h1>
                  Hello, <%= user.firstName %>
                  <span class="wave">👋</span>
                </h1>
                <p>Welcome back to your dashboard</p>
                <div class="time-indicator">
                  <i class="ph ph-clock"></i>
                  <span id="current-time"></span>
                </div>
              </div>
            </div>
            <div class="greeting-decoration">
              <lottie-player
                src="https://assets5.lottiefiles.com/packages/lf20_4kx2q1rf.json"
                background="transparent"
                speed="1"
                style="width: 180px; height: 180px"
                autoplay
                loop
              ></lottie-player>
            </div>
          </div>

          <!-- Summary Section -->
          <div class="summary-section">
            <div class="summary-header">
              <h2>Company Overview</h2>
              <div class="date-range">
                <i class="ph ph-calendar"></i>
                <span><%= moment().format('MMMM YYYY') %></span>
              </div>
            </div>

            <div class="summary-stats">
              <!-- Employee Growth Card -->
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="ph ph-users"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-label">Employee Growth</span>
                  <span class="stat-value"
                    ><%= ((joiners - terminations) / headcount * 100).toFixed(1)
                    %>%</span
                  >
                  <span
                    class="stat-trend <%= (joiners > terminations) ? 'positive' : 'negative' %>"
                  >
                    <i
                      class="ph <%= (joiners > terminations) ? 'ph-trend-up' : 'ph-trend-down' %>"
                    ></i>
                    <%= Math.abs(joiners - terminations) %> this month
                  </span>
                </div>
              </div>

              <!-- Retention Rate Card -->
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="ph ph-chart-line-up"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-label">Retention Rate</span>
                  <span class="stat-value"
                    ><%= ((headcount - terminations) / headcount *
                    100).toFixed(1) %>%</span
                  >
                  <span class="stat-trend">
                    <%= terminations %> departures this month
                  </span>
                </div>
              </div>

              <!-- Add back the Cost Center Distribution card -->
              <div class="stat-item">
                <div class="stat-icon">
                  <i class="ph ph-chart-pie"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-label">Cost Center Distribution</span>
                  <span class="stat-value"
                    ><%= costCenterDistribution.length %></span
                  >
                  <span class="stat-trend">Active cost centers</span>
                </div>
              </div>
            </div>

            <!-- Add back the notification card -->
            <div class="notification-card">
              <div class="notification-header">
                <h3><i class="ph ph-bell"></i> Latest Updates</h3>
                <a href="/notifications" class="view-all-link"
                  >View All <i class="ph ph-arrow-right"></i
                ></a>
              </div>
              <div class="notification-body">
                <% if (latestNotification) { %>
                <div class="notification-item">
                  <div class="notification-meta">
                    <span class="notification-date">
                      <i class="ph ph-clock"></i>
                      <%= moment(latestNotification.createdAt).fromNow() %>
                    </span>
                  </div>
                  <h4 class="notification-title">
                    <%= latestNotification.title %>
                  </h4>
                  <div class="notification-content">
                    <%- latestNotification.content %>
                  </div>
                </div>
                <% } else { %>
                <div class="no-notifications">
                  <i class="ph ph-inbox"></i>
                  <p>No new notifications</p>
                </div>
                <% } %>
              </div>
            </div>

            <!-- Add back the company management card -->
            <div class="company-management-card">
              <div class="management-header">
                <h3><i class="ph ph-buildings"></i> Company Management</h3>
                <a
                  href="/clients/<%= currentCompanyCode %>/settings/employee/employer-details?newCompany=true"
                  class="add-company-btn"
                >
                  <i class="ph ph-plus"></i> Add Company
                </a>
              </div>

              <div class="companies-grid">
                <% if (companies && companies.length > 0) { %> <%
                companies.forEach(function(company) { %>
                <div class="company-card">
                  <div class="company-card-header">
                    <h4><%= company.name %></h4>
                    <span
                      class="company-status <%= company._id.toString() === (currentCompany?._id?.toString() || '') ? 'active' : '' %>"
                    >
                      <%= company._id.toString() ===
                      (currentCompany?._id?.toString() || '') ? 'Current' :
                      'Inactive' %>
                    </span>
                  </div>
                  <div class="company-stats">
                    <div class="stat">
                      <i class="ph ph-users"></i>
                      <span><%= company.employeeCount %> active employees</span>
                    </div>
                  </div>
                  <div class="company-actions">
                    <a href="/companies" class="action-link">
                      <i class="ph ph-gear"></i> Manage
                    </a>
                    <% if (company._id.toString() !==
                    (currentCompany?._id?.toString() || '')) { %>
                    <a href="/companies" class="action-link switch">
                      <i class="ph ph-arrows-clockwise"></i> Switch
                    </a>
                    <% } %>
                  </div>
                </div>
                <% }); %> <% } else { %>
                <div class="no-companies">
                  <i class="ph ph-buildings"></i>
                  <p>No companies added yet</p>
                  <a href="/admin/rfi-wizard" class="btn btn-primary"
                    >Configure First Company</a
                  >
                </div>
                <% } %>
              </div>
            </div>
          </div>

          <!-- Main Cards Grid -->
          <div class="main-cards">
            <!-- Total Headcount -->
            <div class="dashboard-card">
              <div class="card-icon">
                <i class="ph ph-users-three"></i>
              </div>
              <div class="card-content">
                <h3>Total Headcount</h3>
                <div class="card-stats">
                  <span class="number"><%= headcount %></span>
                  <span
                    class="change <%= (joiners > terminations) ? 'positive' : 'negative' %>"
                  >
                    <i
                      class="ph <%= (joiners > terminations) ? 'ph-trend-up' : 'ph-trend-down' %>"
                    ></i>
                    <%= Math.abs(joiners - terminations) %>
                  </span>
                </div>
              </div>
            </div>

            <!-- New Joiners -->
            <div class="dashboard-card">
              <div class="card-icon">
                <i class="ph ph-user-plus"></i>
              </div>
              <div class="card-content">
                <h3>New Joiners</h3>
                <div class="card-stats">
                  <span class="number"><%= joiners %></span>
                  <span class="period">This Month</span>
                </div>
              </div>
            </div>

            <!-- Terminations -->
            <div class="dashboard-card">
              <div class="card-icon">
                <i class="ph ph-user-minus"></i>
              </div>
              <div class="card-content">
                <h3>Terminations</h3>
                <div class="card-stats">
                  <span class="number"><%= terminations %></span>
                  <span class="period">This Month</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Add this before the charts-section -->
          <div class="filter-section">
            <div class="filter-header">
              <h3><i class="ph ph-calendar"></i> Time Period</h3>
            </div>
            <form action="" method="GET" class="filter-form">
              <div class="filter-group">
                <label for="month">
                  <i class="ph ph-calendar-day"></i>
                  Month
                </label>
                <select name="month" id="month" class="filter-select">
                  <% for (let i = 1; i <= 12; i++) { %>
                    <option value="<%= i %>" <%= i === filterMonth ? 'selected' : '' %>>
                      <%= moment().month(i-1).format('MMMM') %>
                    </option>
                  <% } %>
                </select>
              </div>
              <div class="filter-group">
                <label for="year">
                  <i class="ph ph-calendar-blank"></i>
                  Year
                </label>
                <select name="year" id="year" class="filter-select">
                  <% 
                    const currentYear = moment().year();
                    const startYear = currentYear - 5;
                    // Only show next year if we're in Q4 (October onwards)
                    const endYear = moment().quarter() >= 4 ? currentYear + 1 : currentYear;
                    
                    for (let i = endYear; i >= startYear; i--) { 
                  %>
                    <option value="<%= i %>" <%= i === filterYear ? 'selected' : '' %>>
                      <%= i %>
                    </option>
                  <% } %>
                </select>
              </div>
              <button type="submit" class="filter-button">
                <i class="ph ph-arrows-clockwise"></i>
                Update View
              </button>
            </form>
          </div>

          <!-- Charts Section -->
          <div class="charts-section">
            <div class="charts-card">
              <div class="chart-header">
                <h3><i class="ph ph-chart-line"></i> Headcount Trends</h3>
                <div class="chart-legend">
                  <span class="legend-item">
                    <i class="ph ph-circle"></i> Monthly Headcount
                  </span>
                </div>
              </div>
              <div id="bar-chart"></div>
            </div>
          </div>

          <!-- Payroll Calendar Section -->
          <div class="payroll-calendar-section">
            <div class="payroll-calendar-header">
              <h3><i class="ph ph-calendar-check"></i> Payroll Calendar</h3>
              <div class="calendar-actions">
                <button id="addEventBtn" class="btn btn-primary">
                  <i class="ph ph-plus"></i>
                  Add Event
                </button>
                <button id="initializeComplianceBtn" class="btn btn-secondary">
                  <i class="ph ph-gear"></i>
                  Setup Compliance
                </button>
              </div>
            </div>

            <!-- Calendar Stats -->
            <div class="calendar-stats">
              <div class="stat-card">
                <div class="stat-icon pending">
                  <i class="ph ph-clock"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-number" id="pendingCount">-</span>
                  <span class="stat-label">Pending</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon overdue">
                  <i class="ph ph-warning"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-number" id="overdueCount">-</span>
                  <span class="stat-label">Overdue</span>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon this-month">
                  <i class="ph ph-calendar"></i>
                </div>
                <div class="stat-info">
                  <span class="stat-number" id="thisMonthCount">-</span>
                  <span class="stat-label">This Month</span>
                </div>
              </div>
            </div>

            <!-- Calendar Container -->
            <div class="payroll-calendar-container">
              <!-- Calendar Controls -->
              <div class="calendar-controls">
                <div class="filter-controls">
                  <select id="typeFilter" class="filter-select">
                    <option value="all">All Types</option>
                    <option value="cutoff">Cut-off Dates</option>
                    <option value="emp201">EMP201 Monthly</option>
                    <option value="emp501">EMP501 Reconciliation</option>
                    <option value="irp5">IRP5/IT3(a)</option>
                    <option value="uif">UIF</option>
                    <option value="sdl">SDL</option>
                    <option value="eti">ETI</option>
                    <option value="paye">PAYE</option>
                    <option value="compliance">General Compliance</option>
                    <option value="custom">Custom Events</option>
                  </select>
                  <select id="statusFilter" class="filter-select">
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="overdue">Overdue</option>
                  </select>
                </div>

                <!-- Integrated Reminder Controls -->
                <div class="reminder-controls">
                  <div class="reminder-controls-label">
                    <i class="ph ph-bell-ringing"></i>
                    <span>Notifications</span>
                  </div>
                  <div class="reminder-actions">
                    <button id="reminderStatusBtn" class="reminder-action-btn status-btn" title="View reminder status and analytics">
                      <i class="ph ph-chart-line"></i>
                      <span class="btn-label">Status</span>
                    </button>
                    <div class="reminder-divider"></div>
                    <button id="sendRemindersBtn" class="reminder-action-btn send-btn" title="Manually send email reminders">
                      <i class="ph ph-paper-plane"></i>
                      <span class="btn-label">Send Now</span>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Calendar Main Area -->
              <div class="calendar-main-area">
                <!-- Calendar -->
                <div id="payrollCalendar" class="calendar-widget"></div>

                <!-- Upcoming Events List -->
                <div class="upcoming-events">
                  <h4><i class="ph ph-list"></i> Upcoming Events</h4>
                  <div id="upcomingEventsList" class="events-list">
                    <!-- Events will be loaded here -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
      <%- include('partials/mobile-bottom-nav') %>
    </div>

    <!-- Event Management Modals -->
    <!-- Add Event Modal -->
    <div id="addEventModal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="ph ph-plus"></i> Add New Event</h2>
          <button class="modal-close" onclick="closeAddEventModal()">
            <i class="ph ph-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <form id="addEventForm">
            <div class="form-group">
              <label for="eventTitle">
                <i class="ph ph-text-aa"></i> Event Title
              </label>
              <input type="text" id="eventTitle" name="title" required
                     placeholder="Enter event title" class="form-control">
            </div>

            <div class="form-group">
              <label for="eventDate">
                <i class="ph ph-calendar"></i> Date
              </label>
              <input type="date" id="eventDate" name="date" required class="form-control">
            </div>

            <div class="form-group">
              <label for="eventType">
                <i class="ph ph-tag"></i> Event Type
              </label>
              <select id="eventType" name="type" class="form-control">
                <option value="custom">Custom Event</option>
                <option value="cutoff">Payroll Cut-off</option>
                <option value="processing">Processing Deadline</option>
                <option value="payment">Payment Date</option>
                <option value="emp201">EMP201 Monthly Submission</option>
                <option value="emp501">EMP501 Reconciliation</option>
                <option value="irp5">IRP5/IT3(a) Submission</option>
                <option value="uif">UIF Submission</option>
                <option value="sdl">SDL Submission</option>
                <option value="eti">Employment Tax Incentive</option>
                <option value="paye">PAYE Related</option>
                <option value="compliance">General Compliance</option>
                <option value="reminder">Reminder</option>
              </select>
            </div>

            <div class="form-group">
              <label for="eventDescription">
                <i class="ph ph-note"></i> Description (Optional)
              </label>
              <textarea id="eventDescription" name="description"
                        placeholder="Enter event description" class="form-control" rows="3"></textarea>
            </div>

            <div class="form-group">
              <label for="eventPriority">
                <i class="ph ph-flag"></i> Priority
              </label>
              <select id="eventPriority" name="priority" class="form-control">
                <option value="low">Low</option>
                <option value="medium" selected>Medium</option>
                <option value="high">High</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick="closeAddEventModal()">
            <i class="ph ph-x"></i> Cancel
          </button>
          <button type="button" class="btn btn-primary" onclick="submitAddEvent()">
            <i class="ph ph-check"></i> Add Event
          </button>
        </div>
      </div>
    </div>

    <!-- Event Details Modal -->
    <div id="eventDetailsModal" class="modal" style="display: none;">
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="ph ph-calendar-check"></i> Event Details</h2>
          <button class="modal-close" onclick="closeEventDetailsModal()">
            <i class="ph ph-x"></i>
          </button>
        </div>
        <div class="modal-body">
          <div id="eventDetailsContent">
            <!-- Event details will be populated here -->
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" onclick="closeEventDetailsModal()">
            <i class="ph ph-x"></i> Close
          </button>
          <button type="button" class="btn btn-primary" onclick="editCurrentEvent()" style="display: none;">
            <i class="ph ph-pencil"></i> Edit Event
          </button>
        </div>
      </div>
    </div>

    <!-- Keep your existing chart scripts -->
    <script>
      // Chart Configuration
      var barChartOptions = {
          series: [{
            name: 'Headcount',
            data: <%- JSON.stringify(headcountPerMonth.map(item => item.count)) %>
          }],
          chart: {
            type: 'area',
            height: 350,
            toolbar: {
              show: false
            },
            animations: {
              enabled: true,
              easing: 'easeinout',
              speed: 800,
            }
          },
          colors: ['#6366f1'], // Using our primary color
          fill: {
            type: 'gradient',
            gradient: {
              shadeIntensity: 1,
              opacityFrom: 0.7,
              opacityTo: 0.3,
              stops: [0, 90, 100]
            }
          },
          stroke: {
            curve: 'smooth',
            width: 3
          },
          dataLabels: {
            enabled: false
          },
          xaxis: {
            categories: <%- JSON.stringify(headcountPerMonth.map(item =>
              moment(`${item._id.year}-${item._id.month}`, 'YYYY-M').format('MMM YYYY')
            )) %>,
            labels: {
              style: {
                colors: '#64748b', // text-secondary color
                fontSize: '12px'
              }
            }
          },
          yaxis: {
            title: {
              text: 'Headcount',
              style: {
                color: '#64748b'
              }
            },
            labels: {
              formatter: function(value) {
                return Math.round(value);
              }
            }
          },
          grid: {
            borderColor: '#e2e8f0',
            strokeDashArray: 4
          },
          markers: {
            size: 5,
            colors: ['#6366f1'],
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: {
              size: 7
            }
          },
          tooltip: {
            theme: 'light',
            y: {
              formatter: function(value) {
                return Math.round(value) + ' employees';
              }
            }
          }
        };



      // Handle filter form submission
      document.querySelector('.filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const month = document.getElementById('month').value;
        const year = document.getElementById('year').value;
        const currentUrl = new URL(window.location.href);
        
        // Update URL parameters
        currentUrl.searchParams.set('month', month);
        currentUrl.searchParams.set('year', year);
        
        // Reload page with new filters
        window.location.href = currentUrl.toString();
      });

      // Global chart variable
      var barChart;

      // Update chart when filters change
      function updateChart() {
        if (barChart) {
          barChart.updateOptions({
            xaxis: {
              categories: <%- JSON.stringify(headcountPerMonth.map(item =>
                moment(`${item._id.year}-${item._id.month}`, 'YYYY-M').format('MMM YYYY')
              )) %>
            }
          });

          barChart.updateSeries([{
            name: 'Headcount',
            data: <%- JSON.stringify(headcountPerMonth.map(item => item.count)) %>
          }]);
        }
      }

      // ===== PAYROLL CALENDAR FUNCTIONALITY =====

      let payrollCalendar;
      let payrollEvents = [];
      let calendarStats = {
        pending: 0,
        overdue: 0,
        thisMonth: 0
      };

      // Initialize Payroll Calendar
      function initializePayrollCalendar() {
        const calendarEl = document.getElementById('payrollCalendar');

        if (!calendarEl) {
          console.error('Calendar element not found');
          return;
        }

        try {
          payrollCalendar = new FullCalendar.Calendar(calendarEl, {
          initialView: 'dayGridMonth',
          headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,listWeek'
          },
          height: 'auto',
          contentHeight: 'auto',
          aspectRatio: 1.35,
          events: payrollEvents,
          eventClick: function(info) {
            showEventDetails(info.event);
          },
          dateClick: function(info) {
            showAddEventModal(info.date);
          },
          eventDidMount: function(info) {
            // Add custom styling based on event type and status
            const event = info.event;
            const type = event.extendedProps.type;
            const status = event.extendedProps.status;

            // Apply type-based colors
            if (type === 'emp201') {
              info.el.style.backgroundColor = '#ef4444';
              info.el.style.borderColor = '#dc2626';
            } else if (type === 'emp501') {
              info.el.style.backgroundColor = '#dc2626';
              info.el.style.borderColor = '#b91c1c';
            } else if (type === 'irp5' || type === 'it3a') {
              info.el.style.backgroundColor = '#6366f1';
              info.el.style.borderColor = '#4f46e5';
            } else if (type === 'uif') {
              info.el.style.backgroundColor = '#8b5cf6';
              info.el.style.borderColor = '#7c3aed';
            } else if (type === 'sdl') {
              info.el.style.backgroundColor = '#06b6d4';
              info.el.style.borderColor = '#0891b2';
            } else if (type === 'eti') {
              info.el.style.backgroundColor = '#10b981';
              info.el.style.borderColor = '#059669';
            } else if (type === 'paye') {
              info.el.style.backgroundColor = '#f59e0b';
              info.el.style.borderColor = '#d97706';
            } else if (type === 'compliance') {
              info.el.style.backgroundColor = '#6b7280';
              info.el.style.borderColor = '#4b5563';
            } else if (type === 'cutoff') {
              info.el.style.backgroundColor = '#f59e0b';
              info.el.style.borderColor = '#d97706';
            } else if (type === 'custom') {
              info.el.style.backgroundColor = '#22c55e';
              info.el.style.borderColor = '#16a34a';
            }

            // Apply status-based styling
            if (status === 'pending') {
              info.el.style.opacity = '0.8';
              info.el.style.borderStyle = 'dashed';
            } else if (status === 'overdue') {
              info.el.style.opacity = '0.6';
              info.el.classList.add('overdue-event');
            } else if (status === 'completed') {
              info.el.style.opacity = '0.5';
              info.el.style.textDecoration = 'line-through';
            }
          }
        });

        payrollCalendar.render();

        // Force calendar to resize after render
        setTimeout(() => {
          if (payrollCalendar) {
            payrollCalendar.updateSize();
          }
        }, 100);

        } catch (error) {
          console.error('Error creating FullCalendar:', error);
        }
      }

      // Load payroll events from API
      async function loadPayrollEvents() {
        try {
          const response = await fetch('/api/payroll-calendar/events');
          const data = await response.json();

          if (data.success) {
            payrollEvents = data.data.map(event => ({
              id: event._id,
              title: event.title,
              start: event.date,
              end: event.endDate || event.date,
              allDay: true,
              extendedProps: {
                type: event.type,
                status: event.status,
                description: event.description,
                priority: event.priority,
                assignedTo: event.assignedTo
              }
            }));

            if (payrollCalendar) {
              payrollCalendar.removeAllEvents();
              payrollCalendar.addEventSource(payrollEvents);
            }

            updateUpcomingEvents();
          }
        } catch (error) {
          console.error('Error loading payroll events:', error);
        }
      }

      // Load calendar statistics
      async function loadCalendarStats() {
        try {
          const response = await fetch('/api/payroll-calendar/stats');
          const data = await response.json();

          if (data.success) {
            calendarStats = data.data;
            updateStatsDisplay();
          }
        } catch (error) {
          console.error('Error loading calendar stats:', error);
        }
      }

      // Update stats display
      function updateStatsDisplay() {
        document.getElementById('pendingCount').textContent = calendarStats.pending || 0;
        document.getElementById('overdueCount').textContent = calendarStats.overdue || 0;
        document.getElementById('thisMonthCount').textContent = calendarStats.thisMonth || 0;
      }

      // Update upcoming events list
      async function updateUpcomingEvents() {
        try {
          const response = await fetch('/api/payroll-calendar/upcoming');
          const data = await response.json();

          if (data.success) {
            const eventsList = document.getElementById('upcomingEventsList');
            eventsList.innerHTML = '';

            if (data.data.length === 0) {
              eventsList.innerHTML = '<p style="color: #64748b; text-align: center; padding: 1rem;">No upcoming events</p>';
              return;
            }

            data.data.forEach(event => {
              const eventEl = document.createElement('div');
              eventEl.className = 'event-item';

              const eventDate = new Date(event.date);
              const today = new Date();
              const daysUntil = Math.ceil((eventDate - today) / (1000 * 60 * 60 * 24));

              const priorityColors = {
                'critical': '#ef4444',
                'high': '#f59e0b',
                'medium': '#6366f1',
                'low': '#6b7280'
              };

              const typeLabels = {
                'emp201': 'EMP201',
                'emp501': 'EMP501',
                'irp5': 'IRP5/IT3a',
                'it3a': 'IT3a',
                'uif': 'UIF',
                'sdl': 'SDL',
                'eti': 'ETI',
                'paye': 'PAYE',
                'compliance': 'Compliance',
                'cutoff': 'Cut-off',
                'processing': 'Processing',
                'payment': 'Payment',
                'custom': 'Custom',
                'reminder': 'Reminder'
              };

              const priorityIcons = {
                'critical': 'ph-warning-circle',
                'high': 'ph-warning',
                'medium': 'ph-info',
                'low': 'ph-note'
              };

              eventEl.innerHTML = `
                <div class="event-item-header">
                  <div>
                    <div class="event-title">${event.title}</div>
                    <div class="event-date">
                      <i class="ph ph-calendar"></i>
                      ${eventDate.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                      })}
                      ${daysUntil === 0 ? '(Today)' :
                        daysUntil === 1 ? '(Tomorrow)' :
                        daysUntil > 0 ? `(${daysUntil} days)` : '(Overdue)'}
                    </div>
                  </div>
                  <div class="event-type" style="background: ${priorityColors[event.priority || 'medium']}20; color: ${priorityColors[event.priority || 'medium']};">
                    ${typeLabels[event.type] || event.type.toUpperCase()}
                  </div>
                </div>
                <div class="event-priority ${event.priority || 'medium'}">
                  <i class="ph ${priorityIcons[event.priority || 'medium']}"></i>
                  ${(event.priority || 'medium').charAt(0).toUpperCase() + (event.priority || 'medium').slice(1)} Priority
                </div>
              `;

              eventEl.addEventListener('click', () => showEventDetails(event));
              eventsList.appendChild(eventEl);
            });
          }
        } catch (error) {
          console.error('Error loading upcoming events:', error);
        }
      }

      // Show event details modal
      function showEventDetails(event) {
        const modal = document.getElementById('eventDetailsModal');
        const content = document.getElementById('eventDetailsContent');

        const eventDate = new Date(event.start || event.date);
        const typeLabels = {
          'custom': 'Custom Event',
          'cutoff': 'Payroll Cut-off',
          'processing': 'Processing Deadline',
          'payment': 'Payment Date',
          'emp201': 'EMP201 Monthly Submission',
          'emp501': 'EMP501 Reconciliation',
          'irp5': 'IRP5/IT3(a) Submission',
          'it3a': 'IT3(a) Submission',
          'uif': 'UIF Submission',
          'sdl': 'SDL Submission',
          'eti': 'Employment Tax Incentive',
          'paye': 'PAYE Related',
          'compliance': 'General Compliance',
          'reminder': 'Reminder'
        };

        content.innerHTML = `
          <div class="event-details">
            <div class="detail-item">
              <label><i class="ph ph-text-aa"></i> Title:</label>
              <span>${event.title}</span>
            </div>
            <div class="detail-item">
              <label><i class="ph ph-calendar"></i> Date:</label>
              <span>${eventDate.toLocaleDateString()}</span>
            </div>
            <div class="detail-item">
              <label><i class="ph ph-tag"></i> Type:</label>
              <span>${typeLabels[event.extendedProps?.type || event.type] || 'Unknown'}</span>
            </div>
            ${event.extendedProps?.description || event.description ? `
            <div class="detail-item">
              <label><i class="ph ph-note"></i> Description:</label>
              <span>${event.extendedProps?.description || event.description}</span>
            </div>
            ` : ''}
            <div class="detail-item">
              <label><i class="ph ph-flag"></i> Priority:</label>
              <span class="priority-badge priority-${event.extendedProps?.priority || 'medium'}">
                ${(event.extendedProps?.priority || 'medium').charAt(0).toUpperCase() + (event.extendedProps?.priority || 'medium').slice(1)}
              </span>
            </div>
          </div>
        `;

        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);
      }

      // Show add event modal
      function showAddEventModal(date) {
        const modal = document.getElementById('addEventModal');
        const dateInput = document.getElementById('eventDate');

        // Reset form
        document.getElementById('addEventForm').reset();

        // Set the selected date
        if (date) {
          dateInput.value = date.toISOString().split('T')[0];
        }

        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('show'), 10);
      }

      // Close modals
      function closeAddEventModal() {
        const modal = document.getElementById('addEventModal');
        modal.classList.remove('show');
        setTimeout(() => modal.style.display = 'none', 300);
      }

      function closeEventDetailsModal() {
        const modal = document.getElementById('eventDetailsModal');
        modal.classList.remove('show');
        setTimeout(() => modal.style.display = 'none', 300);
      }

      // Submit add event form
      function submitAddEvent() {
        const form = document.getElementById('addEventForm');
        const formData = new FormData(form);

        const eventData = {
          title: formData.get('title'),
          date: formData.get('date'),
          type: formData.get('type'),
          description: formData.get('description'),
          priority: formData.get('priority'),
          status: 'pending'
        };

        // Validate required fields
        if (!eventData.title || !eventData.date) {
          Notifications.error('Please fill in all required fields');
          return;
        }

        addNewEvent(eventData);
        closeAddEventModal();
      }

      // Show confirmation toast with action buttons
      function showConfirmationToast(message, onConfirm, actionText = 'Confirm') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
          toastContainer = document.createElement('div');
          toastContainer.id = 'toast-container';
          toastContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 10000;';
          document.body.appendChild(toastContainer);
        }

        // Create confirmation toast
        const toast = document.createElement('div');
        toast.className = 'toast toast-warning show';
        toast.style.cssText = 'position: relative; margin-bottom: 10px; transform: translateY(0); opacity: 1;';

        toast.innerHTML = `
          <div class="toast-content">
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
              <i class="ph ph-warning" style="font-size: 20px;"></i>
              <span>${message}</span>
            </div>
            <div class="toast-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
              <button class="action-button cancel-btn" style="background: rgba(255,255,255,0.9); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                <i class="ph ph-x"></i> Cancel
              </button>
              <button class="action-button confirm-btn" style="background: rgba(255,255,255,0.9); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold;">
                <i class="ph ph-check"></i> ${actionText}
              </button>
            </div>
          </div>
        `;

        // Add event listeners
        const cancelBtn = toast.querySelector('.cancel-btn');
        const confirmBtn = toast.querySelector('.confirm-btn');

        cancelBtn.addEventListener('click', () => {
          toast.remove();
        });

        confirmBtn.addEventListener('click', () => {
          toast.remove();
          onConfirm();
        });

        toastContainer.appendChild(toast);

        // Auto-remove after 10 seconds
        setTimeout(() => {
          if (toast.parentNode) {
            toast.remove();
          }
        }, 10000);
      }

      // Close modals when clicking outside
      window.addEventListener('click', function(event) {
        const addModal = document.getElementById('addEventModal');
        const detailsModal = document.getElementById('eventDetailsModal');

        if (event.target === addModal) {
          closeAddEventModal();
        }
        if (event.target === detailsModal) {
          closeEventDetailsModal();
        }
      });

      // Close modals with Escape key
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
          const addModal = document.getElementById('addEventModal');
          const detailsModal = document.getElementById('eventDetailsModal');

          if (addModal.classList.contains('show')) {
            closeAddEventModal();
          }
          if (detailsModal.classList.contains('show')) {
            closeEventDetailsModal();
          }
        }
      });

      // Add new event
      async function addNewEvent(eventData) {
        try {
          const response = await fetch('/api/payroll-calendar/events', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(eventData)
          });

          const data = await response.json();

          if (data.success) {
            await loadPayrollEvents();
            await loadCalendarStats();
            Notifications.success('Event added successfully!');
          } else {
            Notifications.error('Error adding event: ' + data.message);
          }
        } catch (error) {
          console.error('Error adding event:', error);
          Notifications.error('Error adding event');
        }
      }

      // Initialize comprehensive South African compliance events
      async function initializeComplianceEvents(force = false) {
        try {
          const year = new Date().getFullYear();
          const response = await fetch('/api/payroll-calendar/initialize-comprehensive-compliance', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ year: year, force: force })
          });

          const data = await response.json();

          if (data.success) {
            await loadPayrollEvents();
            await loadCalendarStats();

            // Show detailed success message
            const details = data.details;
            let message = data.message;
            if (details && details.createdByType) {
              const typesList = Object.entries(details.createdByType)
                .map(([type, count]) => `${count} ${type.toUpperCase()}`)
                .join(', ');
              message += `\n\nCreated events: ${typesList}`;
            }

            Notifications.success(message);
          } else if (data.isDuplicate) {
            // Handle duplicate events with detailed feedback
            handleDuplicateEventsResponse(data);
          } else {
            Notifications.error('Error: ' + data.message);
          }
        } catch (error) {
          console.error('Error initializing compliance events:', error);
          Notifications.error('Error initializing compliance events');
        }
      }

      // Handle duplicate events response with detailed options
      function handleDuplicateEventsResponse(data) {
        const existingTypes = data.data.existingTypes.join(', ');
        const existingCount = data.data.existingCount;

        const message = `Found ${existingCount} existing compliance events for ${data.data.year}.\n\nExisting types: ${existingTypes}\n\nWould you like to:`;

        showCustomConfirmationToast(
          message,
          [
            {
              text: 'Cancel',
              style: 'secondary',
              action: () => {}
            },
            {
              text: 'View Existing Events',
              style: 'info',
              action: () => {
                // Filter calendar to show existing compliance events
                document.getElementById('typeFilter').value = 'all';
                loadPayrollEvents();
                Notifications.info('Showing existing compliance events in calendar');
              }
            },
            {
              text: 'Force Recreate',
              style: 'warning',
              action: () => {
                showConfirmationToast(
                  'This will DELETE all existing comprehensive compliance events and create new ones. This action cannot be undone. Continue?',
                  () => initializeComplianceEvents(true),
                  'Force Recreate Events'
                );
              }
            }
          ],
          'Duplicate Compliance Events Found'
        );
      }

      // Custom confirmation toast with multiple action buttons
      function showCustomConfirmationToast(message, actions, title = 'Confirmation') {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toastId = 'toast-' + Date.now();

        const actionsHtml = actions.map(action =>
          `<button class="btn btn-${action.style} btn-sm me-2" onclick="handleCustomToastAction('${toastId}', ${actions.indexOf(action)})">${action.text}</button>`
        ).join('');

        const toastHtml = `
          <div id="${toastId}" class="toast align-items-center border-0" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="false">
            <div class="d-flex">
              <div class="toast-body">
                <div class="d-flex align-items-start">
                  <div class="me-3">
                    <i class="ph ph-warning-circle text-warning" style="font-size: 1.5rem;"></i>
                  </div>
                  <div class="flex-grow-1">
                    <h6 class="mb-1">${title}</h6>
                    <p class="mb-2 small">${message.replace(/\n/g, '<br>')}</p>
                    <div class="d-flex flex-wrap gap-1">
                      ${actionsHtml}
                    </div>
                  </div>
                </div>
              </div>
              <button type="button" class="btn-close me-2 m-auto" onclick="closeCustomToast('${toastId}')" aria-label="Close"></button>
            </div>
          </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Store actions for later use
        window.customToastActions = window.customToastActions || {};
        window.customToastActions[toastId] = actions;

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
      }

      // Handle custom toast action
      function handleCustomToastAction(toastId, actionIndex) {
        const actions = window.customToastActions[toastId];
        if (actions && actions[actionIndex]) {
          actions[actionIndex].action();
        }
        closeCustomToast(toastId);
      }

      // Close custom toast
      function closeCustomToast(toastId) {
        const toastElement = document.getElementById(toastId);
        if (toastElement) {
          const toast = bootstrap.Toast.getInstance(toastElement);
          if (toast) {
            toast.hide();
          }
          setTimeout(() => {
            toastElement.remove();
            if (window.customToastActions) {
              delete window.customToastActions[toastId];
            }
          }, 300);
        }
      }

      // Event listeners - moved inside DOMContentLoaded
      function attachEventListeners() {
        console.log('🔧 Attaching event listeners...');

        const addEventBtn = document.getElementById('addEventBtn');
        const initializeComplianceBtn = document.getElementById('initializeComplianceBtn');
        const sendRemindersBtn = document.getElementById('sendRemindersBtn');
        const reminderStatusBtn = document.getElementById('reminderStatusBtn');

        console.log('📋 Button elements found:', {
          addEventBtn: !!addEventBtn,
          initializeComplianceBtn: !!initializeComplianceBtn,
          sendRemindersBtn: !!sendRemindersBtn,
          reminderStatusBtn: !!reminderStatusBtn
        });

        if (addEventBtn) {
          addEventBtn.addEventListener('click', () => {
            showAddEventModal(new Date());
          });
          console.log('✅ Add Event button listener attached');
        }

        if (initializeComplianceBtn) {
          initializeComplianceBtn.addEventListener('click', () => {
            // Show confirmation toast with action buttons
            showConfirmationToast(
              'This will create comprehensive South African payroll compliance events including EMP201, EMP501, IRP5/IT3a, UIF, SDL, and other SARS deadlines for the current year. Continue?',
              () => initializeComplianceEvents(),
              'Initialize Compliance Events'
            );
          });
          console.log('✅ Initialize Compliance button listener attached');
        }

        if (sendRemindersBtn) {
          sendRemindersBtn.addEventListener('click', (e) => {
            console.log('📧 Send Reminders button clicked');
            e.preventDefault();
            showConfirmationToast(
              'This will manually trigger email reminders for all upcoming compliance events. Continue?',
              () => sendEmailReminders(),
              'Send Email Reminders'
            );
          });
          console.log('✅ Send Reminders button listener attached');
        } else {
          console.error('❌ Send Reminders button not found');
        }

        if (reminderStatusBtn) {
          reminderStatusBtn.addEventListener('click', (e) => {
            console.log('📊 Reminder Status button clicked');
            e.preventDefault();
            showReminderStatus();
          });
          console.log('✅ Reminder Status button listener attached');
        } else {
          console.error('❌ Reminder Status button not found');
        }
      }

      // Send email reminders manually
      async function sendEmailReminders() {
        console.log('📧 sendEmailReminders function called');

        try {
          console.log('📡 Making API request to /api/payroll-calendar/send-reminders');

          const response = await fetch('/api/payroll-calendar/send-reminders', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            }
          });

          console.log('📡 API Response status:', response.status, response.statusText);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log('📧 API Response data:', data);

          if (data.success) {
            const result = data.data;
            let message = `Email reminders processed successfully!\n\n`;
            message += `✅ Sent: ${result.successCount}\n`;
            message += `❌ Failed: ${result.failureCount}\n`;
            message += `📊 Total Processed: ${result.totalProcessed}`;

            console.log('✅ Showing success notification');
            Notifications.success(message);
          } else {
            console.error('❌ API returned error:', data.message);
            Notifications.error('Error: ' + data.message);
          }
        } catch (error) {
          console.error('❌ Error sending email reminders:', error);
          Notifications.error('Error sending email reminders: ' + error.message);
        }
      }

      // Show reminder status
      async function showReminderStatus() {
        console.log('📊 showReminderStatus function called');

        try {
          console.log('📡 Making API request to /api/payroll-calendar/reminder-status');

          const response = await fetch('/api/payroll-calendar/reminder-status');
          console.log('📡 API Response status:', response.status, response.statusText);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log('📊 API Response data:', data);

          if (data.success) {
            console.log('✅ Displaying reminder status modal');
            displayReminderStatusModal(data.data, data.summary);
          } else {
            console.error('❌ API returned error:', data.message);
            Notifications.error('Error: ' + data.message);
          }
        } catch (error) {
          console.error('❌ Error fetching reminder status:', error);
          Notifications.error('Error fetching reminder status: ' + error.message);
        }
      }

      // Display reminder status in a modal
      function displayReminderStatusModal(reminderData, summary) {
        console.log('📋 displayReminderStatusModal called with:', { reminderData, summary });

        const modalHtml = `
          <div id="reminderStatusModal" class="modal" style="display: block;">
            <div class="modal-content" style="max-width: 800px;">
              <div class="modal-header">
                <h2><i class="ph ph-info"></i> Email Reminder Status</h2>
                <button class="modal-close" onclick="closeReminderStatusModal()">
                  <i class="ph ph-x"></i>
                </button>
              </div>
              <div class="modal-body">
                <div class="reminder-summary mb-4">
                  <h4>Summary</h4>
                  <div class="row">
                    <div class="col-md-4">
                      <div class="stat-card">
                        <div class="stat-number">${summary.totalEvents}</div>
                        <div class="stat-label">Total Events</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="stat-card">
                        <div class="stat-number">${summary.eventsWithPendingReminders}</div>
                        <div class="stat-label">Events with Pending Reminders</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="stat-card">
                        <div class="stat-number">${summary.totalPendingReminders}</div>
                        <div class="stat-label">Total Pending Reminders</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="reminder-details">
                  <h4>Event Details</h4>
                  <div class="table-responsive">
                    <table class="table table-striped">
                      <thead>
                        <tr>
                          <th>Event</th>
                          <th>Date</th>
                          <th>Days Until</th>
                          <th>Sent Reminders</th>
                          <th>Pending Reminders</th>
                          <th>Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        ${reminderData.map(event => `
                          <tr>
                            <td>
                              <div class="event-title">${event.title}</div>
                              <small class="text-muted">${event.type.toUpperCase()}</small>
                            </td>
                            <td>${new Date(event.date).toLocaleDateString()}</td>
                            <td>
                              <span class="badge ${event.daysUntilEvent <= 3 ? 'bg-danger' : event.daysUntilEvent <= 7 ? 'bg-warning' : 'bg-info'}">
                                ${event.daysUntilEvent} days
                              </span>
                            </td>
                            <td>
                              ${event.sentReminders.map(r => `
                                <span class="badge bg-success me-1">${r.daysBeforeEvent}d</span>
                              `).join('')}
                            </td>
                            <td>
                              ${event.pendingReminders.map(r => `
                                <span class="badge bg-warning me-1">${r}d</span>
                              `).join('')}
                            </td>
                            <td>
                              ${event.hasPendingReminders ?
                                '<span class="badge bg-warning">Pending</span>' :
                                '<span class="badge bg-success">Up to date</span>'
                              }
                            </td>
                          </tr>
                        `).join('')}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeReminderStatusModal()">
                  <i class="ph ph-x"></i> Close
                </button>
                ${summary.totalPendingReminders > 0 ? `
                  <button type="button" class="btn btn-primary" onclick="closeReminderStatusModal(); sendEmailReminders();">
                    <i class="ph ph-paper-plane"></i> Send Pending Reminders
                  </button>
                ` : ''}
              </div>
            </div>
          </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        console.log('✅ Reminder status modal inserted into DOM');

        // Verify modal was inserted
        const insertedModal = document.getElementById('reminderStatusModal');
        if (insertedModal) {
          console.log('✅ Modal element found in DOM');
        } else {
          console.error('❌ Modal element not found after insertion');
        }
      }

      // Close reminder status modal
      function closeReminderStatusModal() {
        const modal = document.getElementById('reminderStatusModal');
        if (modal) {
          modal.remove();
        }
      }

      // Filter controls - moved inside attachEventListeners
      function attachCalendarControls() {
        // Filter controls
        const typeFilter = document.getElementById('typeFilter');
        const statusFilter = document.getElementById('statusFilter');

        if (typeFilter) {
          typeFilter.addEventListener('change', (e) => {
            filterEvents();
          });
        }

        if (statusFilter) {
          statusFilter.addEventListener('change', (e) => {
            filterEvents();
          });
        }
      }

      function filterEvents() {
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;

        let filteredEvents = [...payrollEvents];

        if (typeFilter !== 'all') {
          filteredEvents = filteredEvents.filter(event => event.extendedProps.type === typeFilter);
        }

        if (statusFilter !== 'all') {
          filteredEvents = filteredEvents.filter(event => event.extendedProps.status === statusFilter);
        }

        payrollCalendar.removeAllEvents();
        payrollCalendar.addEventSource(filteredEvents);
      }





      // Update time indicator
      function updateTimeIndicator() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
          const now = new Date();
          const timeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          });
          const dateString = now.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
          });
          timeElement.textContent = `${dateString} • ${timeString}`;
        }
      }

      // Initialize everything when page loads
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize time indicator
        updateTimeIndicator();
        setInterval(updateTimeIndicator, 60000); // Update every minute
        // Initialize Charts
        try {
          barChart = new ApexCharts(document.querySelector("#bar-chart"), barChartOptions);
          barChart.render();

          // Initialize chart with current filter values
          updateChart();

          // Add resize handler for charts
          window.addEventListener('resize', function() {
            barChart.updateOptions({
              chart: {
                height: window.innerWidth < 768 ? 300 : 350
              }
            });
          });

          // Add animation on scroll for charts
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                barChart.updateSeries([{
                  data: <%- JSON.stringify(headcountPerMonth.map(item => item.count)) %>
                }]);
              }
            });
          });

          observer.observe(document.querySelector('.charts-card'));
        } catch (error) {
          console.error('Error initializing charts:', error);
        }

        // Always attach event listeners first (critical for reminder functionality)
        try {
          attachEventListeners();
          console.log('✅ Event listeners attached successfully');
        } catch (error) {
          console.error('❌ Error attaching event listeners:', error);
        }

        // Initialize Payroll Calendar with delay to ensure DOM is fully ready
        setTimeout(() => {
          if (typeof FullCalendar !== 'undefined') {
            try {
              initializePayrollCalendar();
              loadPayrollEvents();
              loadCalendarStats();
              attachCalendarControls();
            } catch (error) {
              console.error('Error initializing payroll calendar:', error);

              // Show error message to user
              const calendarEl = document.getElementById('payrollCalendar');
              if (calendarEl) {
                calendarEl.innerHTML = `
                  <div class="alert alert-danger" role="alert">
                    <h5>Calendar Loading Error</h5>
                    <p>There was an error loading the payroll calendar. Please refresh the page or contact support.</p>
                    <small>Error: ${error.message}</small>
                  </div>
                `;
              }
            }
          } else {
            console.error('FullCalendar library not loaded');

            // Show fallback message to user
            const calendarEl = document.getElementById('payrollCalendar');
            if (calendarEl) {
              calendarEl.innerHTML = `
                <div class="alert alert-warning" role="alert">
                  <h5>Calendar Library Not Available</h5>
                  <p>The calendar library failed to load. Please check your internet connection and refresh the page.</p>
                  <button class="btn btn-primary" onclick="location.reload()">Refresh Page</button>
                </div>
              `;
            }
          }
        }, 500);
      });
    </script>

    <!-- Optimized JavaScript Loading - Deferred for Performance -->
    <!-- Load heavy libraries after page render -->
    <script defer src="https://cdnjs.cloudflare.com/ajax/libs/apexcharts/3.35.3/apexcharts.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js"></script>
    <script defer src="https://unpkg.com/@phosphor-icons/web@2.0.3/dist/index.umd.js"></script>
    <script defer src="/js/notifications.js"></script>

    <!-- Performance measurement -->
    <script>
      // Measure performance improvement
      window.addEventListener('load', function() {
        performance.mark('page-loaded');
        performance.measure('page-load-time', 'head-end', 'page-loaded');

        const measure = performance.getEntriesByName('page-load-time')[0];
        console.log('Page load time:', measure.duration + 'ms');

        // Mark content as fully loaded
        document.body.classList.add('content-loaded');
      });
    </script>

    <!-- Include Payment Restriction Script -->
    <%- include('partials/payment-restriction') %>
  </body>
</html>
