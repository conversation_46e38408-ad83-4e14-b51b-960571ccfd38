#!/usr/bin/env node

/**
 * Console Cleanup Testing Script
 * 
 * This script thoroughly tests our cleanup patterns on sample files
 * before applying them to production code.
 */

const fs = require('fs');
const path = require('path');
const ConsoleCleanupPatterns = require('./console-cleanup-patterns');

class CleanupTester {
  constructor() {
    this.patterns = new ConsoleCleanupPatterns();
    this.testDir = path.join(__dirname, '../test-files');
    this.results = [];
    
    this.ensureTestDirectory();
  }

  ensureTestDirectory() {
    if (!fs.existsSync(this.testDir)) {
      fs.mkdirSync(this.testDir, { recursive: true });
    }
  }

  createTestFile(name, content) {
    const filePath = path.join(this.testDir, name);
    fs.writeFileSync(filePath, content);
    return filePath;
  }

  runTest(testName, originalContent, expectedBehavior) {
    console.log(`\n🧪 Testing: ${testName}`);
    console.log('=' .repeat(50));
    
    try {
      // Create test file
      const testFile = this.createTestFile(`${testName.replace(/\s+/g, '_')}.js`, originalContent);
      
      // Apply cleanup
      const cleanedContent = this.patterns.cleanAllPatterns(originalContent, testFile);
      
      // Validate syntax
      const cleanedFile = this.createTestFile(`${testName.replace(/\s+/g, '_')}_cleaned.js`, cleanedContent);
      const syntaxValid = this.patterns.safety.validateSyntax(cleanedFile);
      
      const result = {
        testName,
        originalLines: originalContent.split('\n').length,
        cleanedLines: cleanedContent.split('\n').length,
        linesRemoved: originalContent.split('\n').length - cleanedContent.split('\n').length,
        syntaxValid,
        expectedBehavior,
        passed: syntaxValid
      };
      
      // Check specific expectations
      if (expectedBehavior.shouldPreserveConsoleError !== undefined) {
        result.preservedConsoleError = cleanedContent.includes('console.error');
        result.passed = result.passed && (result.preservedConsoleError === expectedBehavior.shouldPreserveConsoleError);
      }
      
      if (expectedBehavior.shouldRemoveAuthLogging) {
        result.removedAuthLogging = !cleanedContent.includes('Enhanced Authentication Check');
        result.passed = result.passed && result.removedAuthLogging;
      }
      
      if (expectedBehavior.shouldRemoveJWTLogging) {
        result.removedJWTLogging = !cleanedContent.includes('JWT verification result');
        result.passed = result.passed && result.removedJWTLogging;
      }

      if (expectedBehavior.shouldRemoveRouteLogging) {
        result.removedRouteLogging = !cleanedContent.includes('Route Registration Debug');
        result.passed = result.passed && result.removedRouteLogging;
      }
      
      this.results.push(result);
      
      console.log(`✅ Syntax Valid: ${syntaxValid}`);
      console.log(`📊 Lines: ${result.originalLines} → ${result.cleanedLines} (${result.linesRemoved} removed)`);
      console.log(`🎯 Test Passed: ${result.passed}`);

      if (!result.passed) {
        console.log('❌ Test failed - see details:');
        if (expectedBehavior.shouldPreserveConsoleError && !result.preservedConsoleError) {
          console.log('  - Failed to preserve console.error');
        }
        if (expectedBehavior.shouldRemoveAuthLogging && !result.removedAuthLogging) {
          console.log('  - Failed to remove auth logging');
        }
        if (expectedBehavior.shouldRemoveJWTLogging && !result.removedJWTLogging) {
          console.log('  - Failed to remove JWT logging');
        }
        if (expectedBehavior.shouldRemoveRouteLogging && !result.removedRouteLogging) {
          console.log('  - Failed to remove route logging');
        }
      }
      
      return result;
      
    } catch (error) {
      console.log(`❌ Test failed with error: ${error.message}`);
      return {
        testName,
        passed: false,
        error: error.message
      };
    }
  }

  runAllTests() {
    console.log('🚀 Starting Console Cleanup Tests');
    console.log('==================================');
    
    // Test 1: Authentication middleware (critical impact)
    this.runTest('Authentication Middleware', `
const User = require("../models/user");

module.exports = {
  ensureAuthenticated: async (req, res, next) => {
    console.log('\\n=== Enhanced Authentication Check ===');
    console.log('Path:', req.path);
    console.log('Session Auth:', req.isAuthenticated());
    console.log('JWT User:', !!req.jwtUser);
    console.log('Session ID:', req.sessionID);
    console.log('Session Passport:', req.session?.passport);

    if (req.isAuthenticated()) {
      console.log('✓ Session authentication successful');
      return next();
    }

    if (req.jwtUser) {
      console.log('✓ JWT authentication found, attempting to create session');
      return next();
    }

    console.log('✗ No valid authentication found');
    console.error('Critical auth error - this should be preserved');
    res.redirect("/login");
  }
};
    `, {
      shouldPreserveConsoleError: true,
      shouldRemoveAuthLogging: true,
      shouldRemoveJWTLogging: false
    });

    // Test 2: JWT middleware
    this.runTest('JWT Middleware', `
const jwt = require('jsonwebtoken');

const jwtAuth = async (req, res, next) => {
  console.log('\\n=== Any Auth Middleware ===');
  console.log('Request path:', req.path);
  console.log('JWT cookie present:', !!req.cookies.jwt);
  
  const token = req.cookies.jwt;
  if (!token) {
    console.log('No JWT token found');
    return next();
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('JWT verification result:', !!decoded);
    
    if (!decoded) {
      console.log('JWT user not found in database');
      return next();
    }
    
    req.jwtUser = decoded;
    next();
  } catch (error) {
    console.error('JWT verification error:', error);
    next();
  }
};

module.exports = jwtAuth;
    `, {
      shouldPreserveConsoleError: true,
      shouldRemoveJWTLogging: true,
      shouldRemoveAuthLogging: false
    });

    // Test 3: Mixed logging with error handling
    this.runTest('Mixed Logging with Error Handling', `
function processPayroll(data) {
  console.log('Starting payroll processing');
  console.log('Data received:', data);
  
  try {
    // Some processing logic
    const result = calculatePayroll(data);
    console.log('Payroll calculated successfully');
    return result;
  } catch (error) {
    console.error('Payroll calculation failed:', error);
    console.log('Attempting recovery...');
    throw error;
  }
}

module.exports = { processPayroll };
    `, {
      shouldPreserveConsoleError: true,
      shouldRemoveAuthLogging: false,
      shouldRemoveJWTLogging: false
    });

    // Test 4: Email service debugging
    this.runTest('Email Service Debugging', `
const nodemailer = require('nodemailer');

function setupEmailService() {
  console.log('Email server is ready to send messages');
  console.log('Using Zoho SMTP configuration with:', {
    host: 'smtppro.zoho.com',
    port: '587',
    user: '<EMAIL>'
  });
  
  const transporter = nodemailer.createTransporter(config);
  
  transporter.verify((error, success) => {
    if (error) {
      console.error('Email service verification failed:', error);
    } else {
      console.log('Email service verified successfully');
    }
  });
  
  return transporter;
}

module.exports = { setupEmailService };
    `, {
      shouldPreserveConsoleError: true,
      shouldRemoveAuthLogging: false,
      shouldRemoveJWTLogging: false
    });

    // Test 5: Route debugging
    this.runTest('Route Debugging', `
const express = require('express');
const router = express.Router();

router.use((req, res, next) => {
  console.log('\\n=== Route Registration Debug ===');
  console.log('Request URL:', req.originalUrl);
  console.log('Request Method:', req.method);
  console.log('Base URL:', req.baseUrl);
  console.log('Path:', req.path);
  console.log('Params:', req.params);
  next();
});

router.get('/dashboard', (req, res) => {
  console.log('Dashboard route accessed');
  res.render('dashboard');
});

module.exports = router;
    `, {
      shouldPreserveConsoleError: false, // No console.error in this test
      shouldRemoveAuthLogging: false,
      shouldRemoveJWTLogging: false,
      shouldRemoveRouteLogging: true
    });

    // Generate test report
    return this.generateTestReport();
  }

  generateTestReport() {
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('=======================');
    
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`  • ${result.testName}: ${result.error || 'Expectations not met'}`);
      });
    }
    
    const totalLinesRemoved = this.results.reduce((sum, r) => sum + (r.linesRemoved || 0), 0);
    console.log(`\n📈 Total lines that would be removed: ${totalLinesRemoved}`);
    
    if (passedTests === totalTests) {
      console.log('\n✅ ALL TESTS PASSED - Safe to proceed with cleanup!');
      return true;
    } else {
      console.log('\n⚠️  SOME TESTS FAILED - Review and fix before proceeding!');
      return false;
    }
  }

  cleanup() {
    // Remove test files
    if (fs.existsSync(this.testDir)) {
      fs.rmSync(this.testDir, { recursive: true, force: true });
      console.log('\n🧹 Test files cleaned up');
    }
  }
}

// CLI usage
if (require.main === module) {
  const tester = new CleanupTester();
  
  try {
    const allPassed = tester.runAllTests();

    if (allPassed) {
      console.log('\n🎉 Ready to proceed with production cleanup!');
      tester.cleanup();
      process.exit(0);
    } else {
      console.log('\n🛑 Fix test failures before proceeding!');
      tester.cleanup();
      process.exit(1);
    }
  } catch (error) {
    console.error('Test execution failed:', error);
    tester.cleanup();
    process.exit(1);
  }
}

module.exports = CleanupTester;
