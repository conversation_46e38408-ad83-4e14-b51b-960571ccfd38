#!/usr/bin/env node

/**
 * PandaPayroll Console Logging Cleanup Safety Script
 * 
 * This script provides comprehensive safety measures for cleaning up console logging
 * that's causing 1.85-3.15 second performance delays in production.
 * 
 * Safety Features:
 * - Automatic backups before any changes
 * - Syntax validation after each change
 * - Rollback capability
 * - Incremental processing with validation
 * - Comprehensive logging of all operations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ConsoleCleanupSafety {
  constructor() {
    this.backupDir = path.join(__dirname, '../backups/console-cleanup');
    this.logFile = path.join(__dirname, '../logs/console-cleanup.log');
    this.changedFiles = [];
    this.errors = [];
    
    this.ensureDirectories();
  }

  ensureDirectories() {
    [
      path.dirname(this.backupDir),
      this.backupDir,
      path.dirname(this.logFile)
    ].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  log(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${level}: ${message}\n`;
    
    console.log(`${level}: ${message}`);
    fs.appendFileSync(this.logFile, logEntry);
  }

  createBackup(filePath) {
    try {
      const relativePath = path.relative(process.cwd(), filePath);
      const backupPath = path.join(this.backupDir, relativePath);
      const backupDir = path.dirname(backupPath);
      
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }
      
      fs.copyFileSync(filePath, backupPath);
      this.log(`Backup created: ${relativePath} -> ${backupPath}`);
      return backupPath;
    } catch (error) {
      this.log(`Failed to create backup for ${filePath}: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  validateSyntax(filePath) {
    try {
      // For JavaScript files, use Node.js syntax checking
      if (filePath.endsWith('.js')) {
        execSync(`node -c "${filePath}"`, { stdio: 'pipe' });
      }
      
      this.log(`Syntax validation passed: ${path.relative(process.cwd(), filePath)}`);
      return true;
    } catch (error) {
      this.log(`Syntax validation failed: ${path.relative(process.cwd(), filePath)} - ${error.message}`, 'ERROR');
      this.errors.push({
        file: filePath,
        error: error.message,
        type: 'syntax'
      });
      return false;
    }
  }

  rollbackFile(filePath) {
    try {
      const relativePath = path.relative(process.cwd(), filePath);
      const backupPath = path.join(this.backupDir, relativePath);
      
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, filePath);
        this.log(`Rolled back: ${relativePath}`);
        return true;
      } else {
        this.log(`No backup found for rollback: ${relativePath}`, 'ERROR');
        return false;
      }
    } catch (error) {
      this.log(`Failed to rollback ${filePath}: ${error.message}`, 'ERROR');
      return false;
    }
  }

  rollbackAll() {
    this.log('Starting complete rollback of all changes...');
    let rollbackCount = 0;
    
    for (const filePath of this.changedFiles) {
      if (this.rollbackFile(filePath)) {
        rollbackCount++;
      }
    }
    
    this.log(`Rollback completed: ${rollbackCount}/${this.changedFiles.length} files restored`);
    this.changedFiles = [];
    return rollbackCount;
  }

  processFile(filePath, cleanupFunction) {
    try {
      this.log(`Processing file: ${path.relative(process.cwd(), filePath)}`);
      
      // Step 1: Create backup
      this.createBackup(filePath);
      
      // Step 2: Read original content
      const originalContent = fs.readFileSync(filePath, 'utf8');
      
      // Step 3: Apply cleanup function
      const cleanedContent = cleanupFunction(originalContent, filePath);
      
      // Step 4: Check if changes were made
      if (originalContent === cleanedContent) {
        this.log(`No changes needed: ${path.relative(process.cwd(), filePath)}`);
        return { success: true, changed: false };
      }
      
      // Step 5: Write cleaned content
      fs.writeFileSync(filePath, cleanedContent);
      
      // Step 6: Validate syntax
      if (!this.validateSyntax(filePath)) {
        // Rollback on syntax error
        this.rollbackFile(filePath);
        return { success: false, changed: false, error: 'Syntax validation failed' };
      }
      
      // Step 7: Track successful change
      this.changedFiles.push(filePath);
      this.log(`Successfully processed: ${path.relative(process.cwd(), filePath)}`);
      
      return { success: true, changed: true };
      
    } catch (error) {
      this.log(`Error processing ${filePath}: ${error.message}`, 'ERROR');
      this.rollbackFile(filePath);
      return { success: false, changed: false, error: error.message };
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      totalFilesProcessed: this.changedFiles.length,
      errors: this.errors,
      changedFiles: this.changedFiles.map(f => path.relative(process.cwd(), f)),
      backupLocation: this.backupDir,
      logFile: this.logFile
    };
    
    const reportPath = path.join(path.dirname(this.logFile), 'cleanup-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log(`Report generated: ${reportPath}`);
    return report;
  }

  testCleanupFunction(cleanupFunction, testContent) {
    try {
      const result = cleanupFunction(testContent, 'test.js');
      this.log('Cleanup function test passed');
      return { success: true, result };
    } catch (error) {
      this.log(`Cleanup function test failed: ${error.message}`, 'ERROR');
      return { success: false, error: error.message };
    }
  }
}

module.exports = ConsoleCleanupSafety;

// CLI usage
if (require.main === module) {
  const safety = new ConsoleCleanupSafety();
  console.log('Console Cleanup Safety System initialized');
  console.log(`Backup directory: ${safety.backupDir}`);
  console.log(`Log file: ${safety.logFile}`);
}
