#!/usr/bin/env node

/**
 * PandaPayroll Console Logging Cleanup Patterns
 * 
 * This module defines specific patterns for removing console logging
 * while preserving console.error statements and maintaining functionality.
 * 
 * Based on production log analysis showing:
 * - "Enhanced Authentication Check" middleware running on every request
 * - JWT authentication debugging
 * - Email service debugging
 * - Route-specific debugging middleware
 */

const path = require('path');
const ConsoleCleanupSafety = require('./console-cleanup-safety');

class ConsoleCleanupPatterns {
  constructor() {
    this.safety = new ConsoleCleanupSafety();
    
    // High-impact patterns identified from production logs
    this.highImpactPatterns = [
      // Enhanced Authentication Check middleware (biggest impact)
      {
        name: 'Enhanced Authentication Check',
        pattern: /console\.log\(['"`]\\n=== Enhanced Authentication Check ===['"`]\);?\s*\n/g,
        description: 'Remove Enhanced Authentication Check header'
      },
      {
        name: 'Authentication Path Logging',
        pattern: /console\.log\(['"`]Path:['"`],\s*req\.path\);?\s*\n/g,
        description: 'Remove authentication path logging'
      },
      {
        name: 'Session Auth Status',
        pattern: /console\.log\(['"`]Session Auth:['"`],\s*req\.isAuthenticated\(\)\);?\s*\n/g,
        description: 'Remove session auth status logging'
      },
      {
        name: 'JWT User Status',
        pattern: /console\.log\(['"`]JWT User:['"`],\s*!!req\.jwtUser\);?\s*\n/g,
        description: 'Remove JWT user status logging'
      },
      {
        name: 'Session ID Logging',
        pattern: /console\.log\(['"`]Session ID:['"`],\s*req\.sessionID\);?\s*\n/g,
        description: 'Remove session ID logging'
      },
      {
        name: 'Session Passport Logging',
        pattern: /console\.log\(['"`]Session Passport:['"`],\s*req\.session\?\.passport\);?\s*\n/g,
        description: 'Remove session passport logging'
      },
      {
        name: 'Authentication Success',
        pattern: /console\.log\(['"`]✓ Session authentication successful['"`]\);?\s*\n/g,
        description: 'Remove authentication success logging'
      },
      {
        name: 'Authentication Failure',
        pattern: /console\.log\(['"`]✗ No valid authentication found['"`]\);?\s*\n/g,
        description: 'Remove authentication failure logging'
      }
    ];

    // JWT-specific patterns
    this.jwtPatterns = [
      {
        name: 'JWT Middleware Debug',
        pattern: /console\.log\(['"`]\\n=== Any Auth Middleware ===['"`]\);?\s*\n/g,
        description: 'Remove JWT middleware debug header'
      },
      {
        name: 'JWT Verification Result',
        pattern: /console\.log\(['"`]JWT verification result:['"`],\s*!!decoded\);?\s*\n/g,
        description: 'Remove JWT verification result logging'
      },
      {
        name: 'JWT Token Found',
        pattern: /console\.log\(['"`]No JWT token found['"`]\);?\s*\n/g,
        description: 'Remove JWT token not found logging'
      },
      {
        name: 'JWT User Not Found',
        pattern: /console\.log\(['"`]JWT user not found in database['"`]\);?\s*\n/g,
        description: 'Remove JWT user not found logging'
      }
    ];

    // Email service patterns
    this.emailPatterns = [
      {
        name: 'Email Configuration',
        pattern: /console\.log\(['"`]Using Zoho SMTP configuration with:['"`],\s*\{[\s\S]*?\}\);?\s*\n/g,
        description: 'Remove email configuration logging'
      },
      {
        name: 'Email Ready',
        pattern: /console\.log\(['"`]Email server is ready to send messages['"`]\);?\s*\n/g,
        description: 'Remove email ready logging'
      }
    ];

    // Route debugging patterns
    this.routePatterns = [
      {
        name: 'Route Registration Debug',
        pattern: /console\.log\(['"`]\\n=== Route Registration Debug ===['"`]\);?\s*\n/g,
        description: 'Remove route registration debug header'
      },
      {
        name: 'Request URL Logging',
        pattern: /console\.log\(['"`]Request URL:['"`],\s*req\.originalUrl\);?\s*\n/g,
        description: 'Remove request URL logging'
      },
      {
        name: 'Request Method Logging',
        pattern: /console\.log\(['"`]Request Method:['"`],\s*req\.method\);?\s*\n/g,
        description: 'Remove request method logging'
      }
    ];

    // General console.log patterns (excluding console.error)
    this.generalPatterns = [
      {
        name: 'General Console Log',
        pattern: /^(?!.*console\.error).*console\.log\([^)]*\);?\s*\n/gm,
        description: 'Remove general console.log statements (preserve console.error)'
      },
      {
        name: 'Console Info',
        pattern: /console\.info\([^)]*\);?\s*\n/g,
        description: 'Remove console.info statements'
      },
      {
        name: 'Console Warn',
        pattern: /console\.warn\([^)]*\);?\s*\n/g,
        description: 'Remove console.warn statements'
      },
      {
        name: 'Console Debug',
        pattern: /console\.debug\([^)]*\);?\s*\n/g,
        description: 'Remove console.debug statements'
      }
    ];
  }

  // Clean authentication middleware (highest priority)
  cleanAuthenticationMiddleware(content, filePath) {
    let cleaned = content;
    let changeCount = 0;

    this.safety.log(`Cleaning authentication middleware in ${path.basename(filePath)}`);

    // Apply high-impact authentication patterns
    for (const pattern of this.highImpactPatterns) {
      const before = cleaned;
      cleaned = cleaned.replace(pattern.pattern, '');
      if (before !== cleaned) {
        changeCount++;
        this.safety.log(`Applied pattern: ${pattern.name}`);
      }
    }

    this.safety.log(`Authentication cleanup: ${changeCount} patterns applied`);
    return cleaned;
  }

  // Clean JWT middleware
  cleanJWTMiddleware(content, filePath) {
    let cleaned = content;
    let changeCount = 0;

    this.safety.log(`Cleaning JWT middleware in ${path.basename(filePath)}`);

    for (const pattern of this.jwtPatterns) {
      const before = cleaned;
      cleaned = cleaned.replace(pattern.pattern, '');
      if (before !== cleaned) {
        changeCount++;
        this.safety.log(`Applied pattern: ${pattern.name}`);
      }
    }

    this.safety.log(`JWT cleanup: ${changeCount} patterns applied`);
    return cleaned;
  }

  // Clean email service debugging
  cleanEmailService(content, filePath) {
    let cleaned = content;
    let changeCount = 0;

    this.safety.log(`Cleaning email service in ${path.basename(filePath)}`);

    for (const pattern of this.emailPatterns) {
      const before = cleaned;
      cleaned = cleaned.replace(pattern.pattern, '');
      if (before !== cleaned) {
        changeCount++;
        this.safety.log(`Applied pattern: ${pattern.name}`);
      }
    }

    this.safety.log(`Email service cleanup: ${changeCount} patterns applied`);
    return cleaned;
  }

  // Clean route debugging
  cleanRouteDebugging(content, filePath) {
    let cleaned = content;
    let changeCount = 0;

    this.safety.log(`Cleaning route debugging in ${path.basename(filePath)}`);

    for (const pattern of this.routePatterns) {
      const before = cleaned;
      cleaned = cleaned.replace(pattern.pattern, '');
      if (before !== cleaned) {
        changeCount++;
        this.safety.log(`Applied pattern: ${pattern.name}`);
      }
    }

    this.safety.log(`Route debugging cleanup: ${changeCount} patterns applied`);
    return cleaned;
  }

  // Conservative general cleanup (preserves console.error)
  cleanGeneralLogging(content, filePath) {
    let cleaned = content;
    let changeCount = 0;

    this.safety.log(`Cleaning general logging in ${path.basename(filePath)}`);

    // Only apply general patterns if file doesn't contain critical error handling
    const hasCriticalErrorHandling = /catch\s*\([^)]*\)\s*\{[\s\S]*?console\.error/g.test(content);
    
    if (hasCriticalErrorHandling) {
      this.safety.log(`Skipping general cleanup - critical error handling detected`);
      return cleaned;
    }

    for (const pattern of this.generalPatterns) {
      const before = cleaned;
      cleaned = cleaned.replace(pattern.pattern, '');
      if (before !== cleaned) {
        changeCount++;
        this.safety.log(`Applied pattern: ${pattern.name}`);
      }
    }

    this.safety.log(`General cleanup: ${changeCount} patterns applied`);
    return cleaned;
  }

  // Comprehensive cleanup function
  cleanAllPatterns(content, filePath) {
    let cleaned = content;
    
    // Apply in order of priority (highest impact first)
    cleaned = this.cleanAuthenticationMiddleware(cleaned, filePath);
    cleaned = this.cleanJWTMiddleware(cleaned, filePath);
    cleaned = this.cleanEmailService(cleaned, filePath);
    cleaned = this.cleanRouteDebugging(cleaned, filePath);
    cleaned = this.cleanGeneralLogging(cleaned, filePath);
    
    return cleaned;
  }

  // Test patterns with sample content
  testPatterns() {
    const testContent = `
console.log('\\n=== Enhanced Authentication Check ===');
console.log('Path:', req.path);
console.log('Session Auth:', req.isAuthenticated());
console.log('JWT User:', !!req.jwtUser);
console.error('This should be preserved');
console.log('This should be removed');
    `;

    const result = this.safety.testCleanupFunction(
      (content) => this.cleanAllPatterns(content, 'test.js'),
      testContent
    );

    return result;
  }
}

module.exports = ConsoleCleanupPatterns;

// CLI usage
if (require.main === module) {
  const patterns = new ConsoleCleanupPatterns();
  console.log('Testing cleanup patterns...');
  const testResult = patterns.testPatterns();
  console.log('Test result:', testResult);
}
