#!/usr/bin/env node

/**
 * High-Impact Console Logging Analysis
 * 
 * This script analyzes the PandaPayroll codebase to identify and prioritize
 * console logging statements based on their performance impact in production.
 * 
 * Based on production logs showing 1.85-3.15 second delays from console logging.
 */

const fs = require('fs');
const path = require('path');

class HighImpactAnalysis {
  constructor() {
    this.results = {
      criticalFiles: [],
      highImpactFiles: [],
      mediumImpactFiles: [],
      totalConsoleStatements: 0,
      estimatedPerformanceGain: 0
    };
  }

  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      const analysis = {
        path: filePath,
        relativePath: path.relative(process.cwd(), filePath),
        consoleLogCount: 0,
        consoleErrorCount: 0,
        consoleInfoCount: 0,
        consoleWarnCount: 0,
        authenticationLogging: 0,
        jwtLogging: 0,
        routeLogging: 0,
        emailLogging: 0,
        impactLevel: 'LOW',
        estimatedDelayMs: 0,
        patterns: []
      };

      lines.forEach((line, index) => {
        const lineNum = index + 1;
        
        // Count different types of console statements
        if (line.includes('console.log')) analysis.consoleLogCount++;
        if (line.includes('console.error')) analysis.consoleErrorCount++;
        if (line.includes('console.info')) analysis.consoleInfoCount++;
        if (line.includes('console.warn')) analysis.consoleWarnCount++;

        // Identify high-impact patterns from production logs
        if (line.includes('Enhanced Authentication Check')) {
          analysis.authenticationLogging++;
          analysis.patterns.push({ line: lineNum, type: 'CRITICAL_AUTH', content: line.trim() });
        }
        
        if (line.includes('Session Auth:') || line.includes('JWT User:') || line.includes('Session ID:')) {
          analysis.authenticationLogging++;
          analysis.patterns.push({ line: lineNum, type: 'AUTH_DETAIL', content: line.trim() });
        }

        if (line.includes('JWT verification') || line.includes('JWT authentication')) {
          analysis.jwtLogging++;
          analysis.patterns.push({ line: lineNum, type: 'JWT_DEBUG', content: line.trim() });
        }

        if (line.includes('Route Registration Debug') || line.includes('Request URL:')) {
          analysis.routeLogging++;
          analysis.patterns.push({ line: lineNum, type: 'ROUTE_DEBUG', content: line.trim() });
        }

        if (line.includes('Email server') || line.includes('SMTP configuration')) {
          analysis.emailLogging++;
          analysis.patterns.push({ line: lineNum, type: 'EMAIL_DEBUG', content: line.trim() });
        }
      });

      // Calculate impact level and estimated delay
      this.calculateImpact(analysis);
      
      return analysis;
    } catch (error) {
      console.error(`Error analyzing ${filePath}:`, error.message);
      return null;
    }
  }

  calculateImpact(analysis) {
    let impactScore = 0;
    let estimatedDelayMs = 0;

    // Critical impact: Authentication middleware (runs on every request)
    if (analysis.authenticationLogging > 0) {
      impactScore += analysis.authenticationLogging * 10; // High multiplier
      estimatedDelayMs += analysis.authenticationLogging * 50; // 50ms per auth log
    }

    // High impact: JWT middleware
    if (analysis.jwtLogging > 0) {
      impactScore += analysis.jwtLogging * 5;
      estimatedDelayMs += analysis.jwtLogging * 25; // 25ms per JWT log
    }

    // Medium impact: Route debugging
    if (analysis.routeLogging > 0) {
      impactScore += analysis.routeLogging * 3;
      estimatedDelayMs += analysis.routeLogging * 15; // 15ms per route log
    }

    // Medium impact: Email debugging
    if (analysis.emailLogging > 0) {
      impactScore += analysis.emailLogging * 2;
      estimatedDelayMs += analysis.emailLogging * 10; // 10ms per email log
    }

    // General console.log impact
    const generalLogs = analysis.consoleLogCount - analysis.authenticationLogging - analysis.jwtLogging - analysis.routeLogging - analysis.emailLogging;
    if (generalLogs > 0) {
      impactScore += generalLogs * 1;
      estimatedDelayMs += generalLogs * 5; // 5ms per general log
    }

    // Determine impact level
    if (impactScore >= 50) {
      analysis.impactLevel = 'CRITICAL';
    } else if (impactScore >= 20) {
      analysis.impactLevel = 'HIGH';
    } else if (impactScore >= 5) {
      analysis.impactLevel = 'MEDIUM';
    } else {
      analysis.impactLevel = 'LOW';
    }

    analysis.estimatedDelayMs = estimatedDelayMs;
    analysis.impactScore = impactScore;
  }

  analyzeDirectory(dirPath, extensions = ['.js']) {
    const files = this.getFilesRecursively(dirPath, extensions);
    const analyses = [];

    console.log(`Analyzing ${files.length} files in ${dirPath}...`);

    files.forEach(file => {
      const analysis = this.analyzeFile(file);
      if (analysis) {
        analyses.push(analysis);
        this.results.totalConsoleStatements += analysis.consoleLogCount;
        this.results.estimatedPerformanceGain += analysis.estimatedDelayMs;

        // Categorize by impact level
        switch (analysis.impactLevel) {
          case 'CRITICAL':
            this.results.criticalFiles.push(analysis);
            break;
          case 'HIGH':
            this.results.highImpactFiles.push(analysis);
            break;
          case 'MEDIUM':
            this.results.mediumImpactFiles.push(analysis);
            break;
        }
      }
    });

    return analyses;
  }

  getFilesRecursively(dirPath, extensions) {
    const files = [];
    
    if (!fs.existsSync(dirPath)) {
      return files;
    }

    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...this.getFilesRecursively(fullPath, extensions));
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
    
    return files;
  }

  generatePriorityReport() {
    const report = {
      summary: {
        totalFiles: this.results.criticalFiles.length + this.results.highImpactFiles.length + this.results.mediumImpactFiles.length,
        totalConsoleStatements: this.results.totalConsoleStatements,
        estimatedPerformanceGainMs: this.results.estimatedPerformanceGain,
        estimatedPerformanceGainSeconds: (this.results.estimatedPerformanceGain / 1000).toFixed(2)
      },
      priorityOrder: [
        {
          priority: 1,
          level: 'CRITICAL',
          files: this.results.criticalFiles.sort((a, b) => b.impactScore - a.impactScore),
          description: 'Authentication middleware - runs on every request'
        },
        {
          priority: 2,
          level: 'HIGH',
          files: this.results.highImpactFiles.sort((a, b) => b.impactScore - a.impactScore),
          description: 'JWT and route debugging - frequent execution'
        },
        {
          priority: 3,
          level: 'MEDIUM',
          files: this.results.mediumImpactFiles.sort((a, b) => b.impactScore - a.impactScore),
          description: 'General logging - moderate impact'
        }
      ]
    };

    return report;
  }

  printReport() {
    const report = this.generatePriorityReport();
    
    console.log('\n🚨 HIGH-IMPACT CONSOLE LOGGING ANALYSIS');
    console.log('=====================================');
    console.log(`Total files analyzed: ${report.summary.totalFiles}`);
    console.log(`Total console statements: ${report.summary.totalConsoleStatements}`);
    console.log(`Estimated performance gain: ${report.summary.estimatedPerformanceGainSeconds}s`);
    console.log(`Target: 60-70% reduction in 3.75s server response time\n`);

    report.priorityOrder.forEach(priority => {
      if (priority.files.length > 0) {
        console.log(`\n📋 PRIORITY ${priority.priority}: ${priority.level} IMPACT`);
        console.log(`Description: ${priority.description}`);
        console.log(`Files: ${priority.files.length}`);
        
        priority.files.slice(0, 5).forEach(file => {
          console.log(`  • ${file.relativePath}`);
          console.log(`    Impact: ${file.impactScore} | Delay: ${file.estimatedDelayMs}ms | Logs: ${file.consoleLogCount}`);
          
          if (file.patterns.length > 0) {
            console.log(`    Key patterns: ${file.patterns.slice(0, 2).map(p => p.type).join(', ')}`);
          }
        });
        
        if (priority.files.length > 5) {
          console.log(`    ... and ${priority.files.length - 5} more files`);
        }
      }
    });

    return report;
  }
}

// Specific file analysis for known high-impact files
const KNOWN_HIGH_IMPACT_FILES = [
  'config/auth.js',
  'middleware/jwtAuth.js',
  'middleware/debug.js',
  'middleware/sessionDebug.js',
  'app.js'
];

function analyzeKnownFiles() {
  const analysis = new HighImpactAnalysis();
  
  console.log('🎯 Analyzing known high-impact files...\n');
  
  KNOWN_HIGH_IMPACT_FILES.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const result = analysis.analyzeFile(filePath);
      if (result) {
        console.log(`📁 ${result.relativePath}`);
        console.log(`   Impact Level: ${result.impactLevel}`);
        console.log(`   Console Logs: ${result.consoleLogCount}`);
        console.log(`   Auth Logging: ${result.authenticationLogging}`);
        console.log(`   JWT Logging: ${result.jwtLogging}`);
        console.log(`   Estimated Delay: ${result.estimatedDelayMs}ms`);
        console.log(`   Key Patterns: ${result.patterns.length}\n`);
      }
    } else {
      console.log(`❌ File not found: ${filePath}`);
    }
  });
}

module.exports = HighImpactAnalysis;

// CLI usage
if (require.main === module) {
  analyzeKnownFiles();
}
